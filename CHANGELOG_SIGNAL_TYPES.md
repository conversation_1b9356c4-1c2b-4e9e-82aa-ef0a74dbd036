# Changelog: Signal Types Support

## Summary

Added comprehensive support for different signal types (listing, delisting, warning) with enhanced Telegram notifications and bot-specific signal filtering.

## New Features

### 1. Signal Type Detection
- **Listing signals**: `"Market Support for"` pattern → 🟢 icon
- **Delisting signals**: `"TradeNotice on Termination of Trading Support for"` pattern → 🔴 icon  
- **Warning signals**: `"Investment Warning"` pattern → ⚠️ icon

### 2. Enhanced Configuration
- **Bots configuration**: Replaced `accounts` with `bots` for better signal filtering
- **Signal type filtering**: Each bot can specify which signal types to trade on
- **Per-signal-type Telegram channels**: Separate chat configurations for each signal type

### 3. Improved Telegram Messages
- Added emoji icons for visual distinction
- Enhanced message formatting with signal type information
- Support for routing different signal types to different channels

## Configuration Changes

### Before (Legacy)
```json
{
  "accounts": [
    {
      "name": "my bot",
      "exchange": "bybit",
      "direction": "long"
    }
  ],
  "telegram_signals": {
    "enabled": true,
    "telegram_signals_chat": -123456
  }
}
```

### After (New)
```json
{
  "bots": [
    {
      "name": "listing bot",
      "exchange": "bybit", 
      "direction": "long",
      "signal_type": "listing"
    },
    {
      "name": "delisting bot",
      "exchange": "bingx",
      "direction": "short", 
      "signal_type": "delisting"
    }
  ],
  "telegram_signals": {
    "enabled": true,
    "listing": {
      "enabled": true,
      "telegram_signals_chat": -123456
    },
    "delisting": {
      "enabled": true,
      "telegram_signals_chat": -789012
    },
    "warning": {
      "enabled": true,
      "telegram_signals_chat": -345678
    }
  }
}
```

## Code Changes

### New Functions
- `getSignalType(title string) string` - Detects signal type from title
- `isDelistingTitle(title string) bool` - Detects delisting announcements
- `isWarningTitle(title string) bool` - Detects warning announcements
- `handleTradingWithBots()` - Bot-based trading with signal filtering
- `convertBotToAccount()` - Backward compatibility helper

### Enhanced Functions
- `formatSignalMessage()` - Now includes emoji icons and signal type
- `spawnTelegramSignals()` - Routes signals to appropriate channels
- `cacheItem` struct - Added `SignalType` field

### New Configuration Types
- `BotConfig` - Enhanced account config with signal type filtering
- `SignalTypeConfig` - Per-signal-type Telegram configuration
- Enhanced `TelegramSignalsConfig` with signal type support

## Backward Compatibility

- Legacy `accounts` configuration still supported
- Legacy `telegram_signals` configuration still supported
- Automatic migration from accounts to bots when both present
- Existing signal detection logic preserved

## Testing

- All existing tests pass
- New signal type detection tests added
- Trading logic tested with new bot configuration
- Message formatting tested with all signal types

## Migration Path

1. Rename `accounts` to `bots` in config.json
2. Add `signal_type` field to each bot
3. Optionally configure per-signal-type Telegram channels
4. Test with `./upbit-listing test-trading`

## Files Modified

- `main.go` - Core signal processing and trading logic
- `config.go` - Configuration structures and loading
- `config.json` - Example configuration with new format
- Added `SIGNAL_TYPES.md` - Comprehensive documentation

## Example Signal Messages

```
🟢 Signal: API3 listing detected
Title: Market Support for API3(API3) (KRW, USDT Market)
Time: 2025-08-24 18:38:55 MSK

🔴 Signal: PUNDIAI delisting detected  
Title: TradeNotice on Termination of Trading Support for Pundi AI(PUNDIAI)
Time: 2025-08-24 18:38:55 MSK

⚠️ Signal: PUNDIAI warning detected
Title: Investment Warning (PUNDIAI)
Time: 2025-08-24 18:38:55 MSK
```
