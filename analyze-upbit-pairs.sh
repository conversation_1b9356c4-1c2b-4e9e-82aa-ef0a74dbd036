#!/bin/bash

echo "========================================"
echo "Upbit Pairs Analysis Tool"
echo "========================================"
echo ""
echo "This script analyzes Upbit trading pairs to find coins"
echo "that don't have KRW market but trade in USDT/BTC pairs."
echo ""

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "ERROR: Go is not installed or not in PATH"
    echo "Please install Go from https://golang.org/dl/"
    exit 1
fi

echo "Running analysis..."
echo ""

# Run the Go script
go run cmd/analyze-upbit-pairs/main.go

if [ $? -ne 0 ]; then
    echo ""
    echo "ERROR: Analysis failed"
    exit 1
fi

echo ""
echo "========================================"
echo "Analysis completed successfully!"
echo "Results saved to: upbit_pairs_analysis.json"
echo "========================================"
echo ""

# Check if the result file exists and show some stats
if [ -f "upbit_pairs_analysis.json" ]; then
    echo "Result file created successfully."
    echo ""
    echo "Quick stats:"
    if command -v jq &> /dev/null; then
        echo "Total coins: $(jq '.total_coins' upbit_pairs_analysis.json)"
        echo "Coins without KRW: $(jq '.coins_without_krw_count' upbit_pairs_analysis.json)"
        echo ""
        echo "Top 10 coins without KRW market:"
        jq -r '.coins_without_krw[:10][] | "- \(.symbol): \(.available_quotes | join(", "))"' upbit_pairs_analysis.json
    else
        echo "Install 'jq' for better JSON parsing and stats display"
        echo "You can view the full results in: upbit_pairs_analysis.json"
    fi
else
    echo "Warning: Result file not found"
fi

echo ""
echo "Done!"
