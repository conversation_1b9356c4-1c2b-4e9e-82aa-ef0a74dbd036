# Signal Types Support

This document describes the new signal types support added to the upbit-listing project.

## Overview

The project now supports three types of signals:
- **Listing** (`listing`) - New token listings (Market Support announcements)
- **Delisting** (`delisting`) - Token delistings (TradeNotice on Termination announcements)
- **Warning** (`warning`) - Investment warnings

## Configuration Changes

### Bots Configuration

The configuration now uses `bots` instead of `accounts` to support signal type filtering:

```json
{
  "bots": [
    {
      "name": "suenot: bybit listing",
      "exchange": "bybit",
      "signal_type": "listing",
      "direction": "long",
      "enabled": true,
      "api_key": "${BYBIT_API_KEY}",
      "api_secret": "${BYBIT_API_SECRET}",
      "amount_usdt": 20,
      "leverage": 20,
      "tp": [10],
      "sl": [10],
      "trailing_enabled": true,
      "trailing_percent": 10,
      "telegram_enabled": true,
      "telegram_logs_chat": -**********,
      "telegram_bot_token": "${TELEGRAM_BOT_TOKEN}"
    },
    {
      "name": "suenot: bingx delisting",
      "exchange": "bingx",
      "signal_type": "delisting",
      "direction": "short",
      "enabled": true,
      "api_key": "${BINGX_API_KEY}",
      "api_secret": "${BINGX_API_SECRET}",
      "amount_usdt": 20,
      "leverage": 20,
      "tp": [10, 20, 30],
      "sl": [10],
      "trailing_enabled": true,
      "trailing_percent": 20,
      "telegram_enabled": true,
      "telegram_logs_chat": -**********,
      "telegram_bot_token": "${TELEGRAM_BOT_TOKEN}"
    }
  ]
}
```

### Signal Type Options

- `"listing"` - Only trade on listing signals
- `"delisting"` - Only trade on delisting signals  
- `"warning"` - Only trade on warning signals
- `"all"` - Trade on all signal types
- `""` (empty) - Trade on all signal types (default)

### Telegram Signals Configuration

Enhanced telegram signals configuration with per-signal-type settings:

```json
{
  "telegram_signals": {
    "enabled": true,
    "telegram_bot_token": "${TELEGRAM_BOT_TOKEN}",
    "telegram_signals_chat": -4952846534,
    "listing": {
      "enabled": true,
      "telegram_signals_chat": -4952846534
    },
    "delisting": {
      "enabled": true,
      "telegram_signals_chat": -4952846534
    },
    "warning": {
      "enabled": true,
      "telegram_signals_chat": -4952846534
    }
  }
}
```

## Signal Detection

### Listing Signals
- Pattern: `"Market Support for"`
- Example: `"Market Support for API3(API3) (KRW, USDT Market)"`
- Icon: 🟢 (green circle)

### Delisting Signals
- Pattern: `"TradeNotice on Termination of Trading Support for"`
- Example: `"TradeNotice on Termination of Trading Support for Pundi AI(PUNDIAI)"`
- Icon: 🔴 (red circle)

### Warning Signals
- Pattern: `"Investment Warning"`
- Example: `"Investment Warning (PUNDIAI)"`
- Icon: ⚠️ (warning sign)

## Message Format

Signal messages now include icons and improved formatting:

```
🟢 Signal: API3 listing detected
Title: Market Support for API3(API3) (KRW, USDT Market)
Time: 2025-08-24 18:38:55 MSK

🔴 Signal: PUNDIAI delisting detected
Title: TradeNotice on Termination of Trading Support for Pundi AI(PUNDIAI)
Time: 2025-08-24 18:38:55 MSK

⚠️ Signal: PUNDIAI warning detected
Title: Investment Warning (PUNDIAI)
Time: 2025-08-24 18:38:55 MSK
```

## Backward Compatibility

- Legacy `accounts` configuration is still supported
- If both `bots` and `accounts` are present, `bots` takes precedence
- Legacy telegram signals configuration without signal type separation is supported

## Testing

Test the new functionality:

```bash
# Test signal type detection and formatting
./upbit-listing test-signals

# Test trading with new bot configuration
./upbit-listing test-trading
```

## Migration Guide

To migrate from `accounts` to `bots`:

1. Rename `accounts` to `bots` in your config.json
2. Add `signal_type` field to each bot configuration
3. Update telegram signals configuration if you want separate channels
4. Test with `./upbit-listing test-signals`

Example migration:

```json
// Before
{
  "accounts": [
    {
      "name": "my bot",
      "exchange": "bybit",
      "direction": "long"
    }
  ]
}

// After
{
  "bots": [
    {
      "name": "my bot",
      "exchange": "bybit", 
      "direction": "long",
      "signal_type": "listing"
    }
  ]
}
```
