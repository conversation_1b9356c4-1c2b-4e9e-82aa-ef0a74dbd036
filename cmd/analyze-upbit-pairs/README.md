# Analyze Upbit Pairs

Этот скрипт анализирует торговые пары на бирже Upbit и находит монеты, которые не имеют пары с KRW (корейской воной), но торгуются в других парах (USDT, BTC).

## Описание

Скрипт выполняет следующие действия:

1. Получает список всех торговых пар с Upbit API
2. Группирует пары по базовой валюте (символу монеты)
3. Анализирует, какие котировочные валюты доступны для каждой монеты
4. Находит монеты, которые не имеют пары с KRW
5. Сохраняет результат в JSON файл в корне проекта

## Использование

```bash
# Из корня проекта
go run cmd/analyze-upbit-pairs/main.go
```

## Выходной файл

Результат сохраняется в файл `upbit_pairs_analysis.json` в корне проекта со следующей структурой:

```json
{
  "timestamp": "2024-08-24T12:00:00Z",
  "coins_without_krw": [
    {
      "symbol": "EXAMPLE",
      "available_quotes": ["BTC", "USDT"],
      "has_krw": false,
      "has_usdt": true,
      "has_btc": true
    }
  ],
  "total_coins": 150,
  "coins_without_krw_count": 25,
  "summary": "Found 25 coins without KRW market out of 150 total coins"
}
```

## Поля результата

- `timestamp` - время выполнения анализа
- `coins_without_krw` - массив монет без KRW пары
- `total_coins` - общее количество уникальных монет на Upbit
- `coins_without_krw_count` - количество монет без KRW пары
- `summary` - краткое описание результатов

Для каждой монеты без KRW пары указывается:
- `symbol` - символ монеты
- `available_quotes` - доступные котировочные валюты
- `has_krw` - есть ли пара с KRW (всегда false для этого списка)
- `has_usdt` - есть ли пара с USDT
- `has_btc` - есть ли пара с BTC

## Зависимости

Скрипт использует только стандартную библиотеку Go и не требует дополнительных зависимостей.
