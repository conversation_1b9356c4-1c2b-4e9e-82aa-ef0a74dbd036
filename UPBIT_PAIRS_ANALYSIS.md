# Upbit Pairs Analysis

Этот инструмент анализирует торговые пары на бирже Upbit и находит монеты, которые не имеют пары с KRW (корейской воной), но торгуются в других парах (USDT, BTC).

## Быстрый запуск

### Windows (CMD)
```cmd
analyze-upbit-pairs.bat
```

### Linux/macOS (Terminal)
```bash
./analyze-upbit-pairs.sh
```

### Прямой запуск через Go
```bash
go run cmd/analyze-upbit-pairs/main.go
```

### Использование скомпилированного бинарника
```bash
# Сначала скомпилировать
go build -o analyze-upbit-pairs cmd/analyze-upbit-pairs/main.go

# Затем запустить
./analyze-upbit-pairs
```

## Результат

Скрипт создает файл `upbit_pairs_analysis.json` в корне проекта со следующей информацией:

- **Список монет без KRW пары** - все монеты, которые торгуются только в парах USDT/BTC
- **Доступные рынки для каждой монеты** - показывает, в каких именно парах торгуется монета
- **Статистика** - общее количество монет и количество монет без KRW пары

## Пример результата

```json
{
  "timestamp": "2024-08-24T15:52:57Z",
  "coins_without_krw": [
    {
      "symbol": "BRETT",
      "available_quotes": ["USDT"],
      "has_krw": false,
      "has_usdt": true,
      "has_btc": false
    },
    {
      "symbol": "JASMY",
      "available_quotes": ["BTC", "USDT"],
      "has_krw": false,
      "has_usdt": true,
      "has_btc": true
    }
  ],
  "total_coins": 261,
  "coins_without_krw_count": 72,
  "summary": "Found 72 coins without KRW market out of 261 total coins"
}
```

## Интерпретация результатов

### Типы монет без KRW пары:

1. **Только USDT** - монеты, торгующиеся только против USDT
   - Пример: `BRETT`, `EPT`
   - Это может указывать на новые листинги или специфические монеты

2. **Только BTC** - монеты, торгующиеся только против BTC
   - Пример: `ACM`, `AFC`, `APE`
   - Часто это старые или менее популярные монеты

3. **BTC + USDT** - монеты с обеими парами, но без KRW
   - Пример: `JASMY`, `ARPA`, `DGB`
   - Это международные монеты, которые еще не получили KRW пару

### Потенциальные кандидаты для KRW листинга

Монеты с парами `BTC + USDT` являются наиболее вероятными кандидатами для получения KRW пары в будущем, так как они уже имеют достаточную ликвидность на бирже.

## Требования

- Go 1.19 или выше
- Интернет-соединение для доступа к Upbit API

## Файлы проекта

- `cmd/analyze-upbit-pairs/main.go` - основной код анализатора
- `cmd/analyze-upbit-pairs/README.md` - подробная документация
- `analyze-upbit-pairs.bat` - скрипт для Windows
- `analyze-upbit-pairs.sh` - скрипт для Linux/macOS
- `upbit_pairs_analysis.json` - результат анализа (создается после запуска)
