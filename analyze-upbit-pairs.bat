@echo off
echo ========================================
echo Upbit Pairs Analysis Tool
echo ========================================
echo.
echo This script analyzes Upbit trading pairs to find coins
echo that don't have KRW market but trade in USDT/BTC pairs.
echo.

REM Check if Go is installed
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Go is not installed or not in PATH
    echo Please install Go from https://golang.org/dl/
    pause
    exit /b 1
)

echo Running analysis...
echo.

REM Run the Go script
go run cmd/analyze-upbit-pairs/main.go

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Analysis failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Analysis completed successfully!
echo Results saved to: upbit_pairs_analysis.json
echo ========================================
echo.

REM Check if the result file exists and show some stats
if exist upbit_pairs_analysis.json (
    echo Opening result file...
    start notepad upbit_pairs_analysis.json
) else (
    echo Warning: Result file not found
)

pause
