{"bots": [{"amount_usdt": 20, "api_key": "${BYBIT_API_KEY}", "api_secret": "${BYBIT_API_SECRET}", "direction": "long", "enabled": true, "exchange": "bybit", "leverage": 20, "name": "suenot: bybit listing", "signal_type": "listing", "sl": [10], "telegram_bot_token": "${TELEGRAM_BOT_TOKEN}", "telegram_enabled": true, "telegram_logs_chat": -4972872730, "tp": [10], "trailing_enabled": true, "trailing_percent": 10}, {"amount_usdt": 20, "api_key": "${BINGX_API_KEY}", "api_secret": "${BINGX_API_SECRET}", "direction": "long", "enabled": true, "exchange": "bingx", "leverage": 20, "name": "suenot: bingx listing", "signal_type": "listing", "sl": [10], "telegram_bot_token": "${TELEGRAM_BOT_TOKEN}", "telegram_enabled": true, "telegram_logs_chat": -4972872730, "tp": [10, 20, 30], "trailing_enabled": true, "trailing_percent": 20}, {"amount_usdt": 20, "api_key": "${BYBIT_API_KEY}", "api_secret": "${BYBIT_API_SECRET}", "direction": "short", "enabled": true, "exchange": "bybit", "leverage": 20, "name": "suenot: bybit delisting", "signal_type": "delisting", "sl": [10], "telegram_bot_token": "${TELEGRAM_BOT_TOKEN}", "telegram_enabled": true, "telegram_logs_chat": -4972872730, "tp": [10], "trailing_enabled": true, "trailing_percent": 10}, {"amount_usdt": 20, "api_key": "${BINGX_API_KEY}", "api_secret": "${BINGX_API_SECRET}", "direction": "short", "enabled": true, "exchange": "bingx", "leverage": 20, "name": "suenot: bingx delisting", "signal_type": "delisting", "sl": [10], "telegram_bot_token": "${TELEGRAM_BOT_TOKEN}", "telegram_enabled": true, "telegram_logs_chat": -4972872730, "tp": [10, 20, 30], "trailing_enabled": true, "trailing_percent": 20}], "proxies": ["http:***************:63255:VZMp5PKM:53BYByBB"], "settings": {"announcements_api": {"cycle_interval_ms": 4800, "enabled": true, "forced_parsing": {"after_ms": 60000, "before_ms": 60000, "enabled": true, "request_interval_ms": 500}, "max_attempts": 8, "max_retry_after_sec": 100, "page_delay_jitter_ms": 100, "page_delay_ms": 1000, "proxy": true}, "api": {"playground": "prod", "urls": {"dev": {"announcements_api": "http://localhost:8080/api/v1/announcements", "markets_api": "http://localhost:8080/v1/market/all", "service_center": "http://localhost:8080/service_center/notice"}, "prod": {"announcements_api": "https://api-manager.upbit.com/api/v1/announcements", "markets_api": "https://api.upbit.com/v1/market/all", "service_center": "https://upbit.com/service_center/notice"}}}, "cycle": true, "interval_tuning": {"check_period_minutes": 60, "enabled": true, "max_interval_ms": 5000, "max_rollback_count": 10, "min_interval_ms": 200, "tune_step_ms": 100}, "markets_api": {"enabled": true, "forced_parsing": {"after_ms": 60000, "before_ms": 60000, "enabled": false, "request_interval_ms": 500}, "interval_ms": 4800, "max_attempts": 5, "proxy": false, "timeout_sec": 30}, "service_center": {"enabled": false, "forced_parsing": {"after_ms": 60000, "before_ms": 60000, "enabled": false, "request_interval_ms": 500}, "interval_ms": 4800, "max_attempts": 3, "proxy": false, "timeout_sec": 30}, "trade": true}, "telegram_signals": {"delisting": {"enabled": true, "telegram_signals_chat": -4952846534}, "enabled": true, "listing": {"enabled": true, "telegram_signals_chat": -4952846534}, "telegram_bot_token": "${TELEGRAM_BOT_TOKEN}", "telegram_signals_chat": -4952846534, "warning": {"enabled": true, "telegram_signals_chat": -4952846534}}}